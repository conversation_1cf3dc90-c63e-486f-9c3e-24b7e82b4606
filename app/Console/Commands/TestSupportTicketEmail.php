<?php

namespace App\Console\Commands;

use App\Mail\NewSupportTicketMail;
use App\Models\SupportTicket;
use App\Models\User;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Mail;

class TestSupportTicketEmail extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:support-ticket-email {--user-id=1}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test support ticket email notification';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $userId = $this->option('user-id');
        $user = User::find($userId);

        if (!$user) {
            $this->error("User with ID {$userId} not found.");
            return 1;
        }

        // Create a test support ticket
        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'subject' => 'Test Email Support Ticket',
            'description' => 'This is a test ticket created to verify email notifications are working correctly.',
            'priority' => 'medium',
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $this->info("Created test ticket #" . $ticket->id);

        // Send test email
        try {
            $adminEmail = config('mail.admin_email', env('ADMIN_EMAIL', '<EMAIL>'));
            $this->info("Sending email to: " . $adminEmail);

            Mail::to($adminEmail)->send(new NewSupportTicketMail($ticket));

            $this->info("✅ Email sent successfully!");
            $this->info("Check your email inbox at: " . $adminEmail);

        } catch (\Exception $e) {
            $this->error("❌ Failed to send email: " . $e->getMessage());
            return 1;
        }

        return 0;
    }
}
