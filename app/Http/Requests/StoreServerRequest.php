<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreServerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'api_link' => 'required|url|max:500',
            'gateway_type' => 'required|string|exists:gateway_provider_types,name',
            'status' => 'required|in:enabled,disabled',

            // Authentication validation - flexible for custom providers
            'api_key' => 'nullable|string|max:500',
            'username' => 'nullable|string|max:255|required_with:password',
            'password' => 'nullable|string|max:255|required_with:username',

            // Optional fields
            'sending_limit' => 'nullable|integer|min:1',
            'time_base' => 'nullable|integer|min:1',
            'time_unit' => 'nullable|string|in:minute,hour,day',
            'api_parameters' => 'nullable|json',
        ];
    }

    /**
     * Get custom validation messages.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $apiKey = $this->input('api_key');
            $username = $this->input('username');
            $password = $this->input('password');

            // Ensure at least one authentication method is provided
            if (empty($apiKey) && (empty($username) || empty($password))) {
                $validator->errors()->add('authentication', 'Please provide either an API Key or Username & Password for authentication.');
            }
        });
    }

    public function messages(): array
    {
        return [
            'username.required_with' => 'Username is required when Password is provided.',
            'password.required_with' => 'Password is required when Username is provided.',
            'gateway_type.exists' => 'The selected gateway provider type is invalid.',
        ];
    }
}
