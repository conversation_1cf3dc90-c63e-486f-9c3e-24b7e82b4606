<?php

namespace App\Http\Requests;

use App\Models\SupportTicket;
use Illuminate\Foundation\Http\FormRequest;

class UpdateSupportTicketRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $ticket = $this->route('supportTicket');

        // Users can only update their own tickets if they're open
        if (auth()->user()->hasAnyRole(['super-admin', 'master-reseller'])) {
            return true; // Admins can update any ticket
        }

        return $ticket &&
               $ticket->user_id === auth()->id() &&
               $ticket->status === SupportTicket::STATUS_OPEN;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $isAdmin = auth()->user()->hasAnyRole(['super-admin', 'master-reseller']);

        if ($isAdmin) {
            // Admin validation rules
            return [
                'status' => [
                    'required',
                    'string',
                    'in:' . implode(',', array_keys(SupportTicket::getStatuses())),
                ],
                'priority' => [
                    'required',
                    'string',
                    'in:' . implode(',', array_keys(SupportTicket::getPriorities())),
                ],
                'assigned_to' => [
                    'nullable',
                    'exists:users,id',
                ],
                'admin_notes' => [
                    'nullable',
                    'string',
                    'max:5000',
                ],
            ];
        } else {
            // User validation rules (can only edit subject, description, priority)
            return [
                'subject' => [
                    'required',
                    'string',
                    'max:255',
                    'min:5',
                ],
                'description' => [
                    'required',
                    'string',
                    'min:10',
                    'max:5000',
                ],
                'priority' => [
                    'required',
                    'string',
                    'in:' . implode(',', array_keys(SupportTicket::getPriorities())),
                ],
            ];
        }
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'subject.required' => 'Please provide a subject for your support ticket.',
            'subject.min' => 'The subject must be at least 5 characters long.',
            'subject.max' => 'The subject cannot exceed 255 characters.',
            'description.required' => 'Please describe your issue in detail.',
            'description.min' => 'The description must be at least 10 characters long.',
            'description.max' => 'The description cannot exceed 5000 characters.',
            'priority.required' => 'Please select a priority level.',
            'priority.in' => 'Please select a valid priority level.',
            'status.required' => 'Please select a status.',
            'status.in' => 'Please select a valid status.',
            'assigned_to.exists' => 'The selected admin user does not exist.',
            'admin_notes.max' => 'Admin notes cannot exceed 5000 characters.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'subject' => 'ticket subject',
            'description' => 'issue description',
            'priority' => 'priority level',
            'status' => 'ticket status',
            'assigned_to' => 'assigned admin',
            'admin_notes' => 'admin response',
        ];
    }
}
