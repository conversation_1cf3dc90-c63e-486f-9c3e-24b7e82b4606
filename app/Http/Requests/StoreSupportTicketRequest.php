<?php

namespace App\Http\Requests;

use App\Models\SupportTicket;
use Illuminate\Foundation\Http\FormRequest;

class StoreSupportTicketRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'subject' => [
                'required',
                'string',
                'max:255',
                'min:5',
            ],
            'description' => [
                'required',
                'string',
                'min:10',
                'max:5000',
            ],
            'priority' => [
                'required',
                'string',
                'in:' . implode(',', array_keys(SupportTicket::getPriorities())),
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'subject.required' => 'Please provide a subject for your support ticket.',
            'subject.min' => 'The subject must be at least 5 characters long.',
            'subject.max' => 'The subject cannot exceed 255 characters.',
            'description.required' => 'Please describe your issue in detail.',
            'description.min' => 'The description must be at least 10 characters long.',
            'description.max' => 'The description cannot exceed 5000 characters.',
            'priority.required' => 'Please select a priority level for your ticket.',
            'priority.in' => 'Please select a valid priority level.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'subject' => 'ticket subject',
            'description' => 'issue description',
            'priority' => 'priority level',
        ];
    }
}
