<?php

namespace App\Notifications;

use App\Models\SupportTicket;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

class NewSupportTicketNotification extends Notification implements ShouldQueue
{
    use Queueable;

    protected $supportTicket;

    /**
     * Create a new notification instance.
     */
    public function __construct(SupportTicket $supportTicket)
    {
        $this->supportTicket = $supportTicket;
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array
    {
        return ['mail'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $ticket = $this->supportTicket;
        $user = $ticket->user;

        return (new MailMessage)
            ->subject('New Support Ticket Created - #' . $ticket->id)
            ->greeting('Hello Admin!')
            ->line('A new support ticket has been created and requires your attention.')
            ->line('')
            ->line('**Ticket Details:**')
            ->line('**Ticket ID:** #' . $ticket->id)
            ->line('**Subject:** ' . $ticket->subject)
            ->line('**Priority:** ' . ucfirst($ticket->priority))
            ->line('**Status:** ' . ucfirst(str_replace('_', ' ', $ticket->status)))
            ->line('')
            ->line('**Customer Information:**')
            ->line('**Name:** ' . $user->name)
            ->line('**Email:** ' . $user->email)
            ->line('**Phone:** ' . $user->phone)
            ->line('**Role:** ' . ($user->roles->first()->name ?? 'N/A'))
            ->line('')
            ->line('**Issue Description:**')
            ->line($ticket->description)
            ->line('')
            ->action('View Ticket', route('admin.support-tickets.show', $ticket->id))
            ->line('Please respond to this ticket as soon as possible to maintain customer satisfaction.')
            ->line('')
            ->line('Best regards,')
            ->line(config('app.name') . ' Support System');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'ticket_id' => $this->supportTicket->id,
            'ticket_subject' => $this->supportTicket->subject,
            'ticket_priority' => $this->supportTicket->priority,
            'customer_name' => $this->supportTicket->user->name,
            'customer_email' => $this->supportTicket->user->email,
            'created_at' => $this->supportTicket->created_at,
        ];
    }
}
