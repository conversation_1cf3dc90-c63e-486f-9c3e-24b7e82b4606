<?php

namespace App\Models;

use Illuminate\Notifications\Notifiable;

class AdminNotifiable
{
    use Notifiable;

    public $email;
    public $name;

    public function __construct()
    {
        $this->email = config('mail.admin_email', env('ADMIN_EMAIL', '<EMAIL>'));
        $this->name = 'Admin';
    }

    /**
     * Route notifications for the mail channel.
     *
     * @return string
     */
    public function routeNotificationForMail()
    {
        return $this->email;
    }
}
