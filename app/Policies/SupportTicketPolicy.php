<?php

namespace App\Policies;

use App\Models\SupportTicket;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class SupportTicketPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasPermissionTo('ticket-list');
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, SupportTicket $supportTicket): bool
    {
        // Admins can view all tickets
        if ($user->hasAnyRole(['super-admin', 'master-reseller'])) {
            return true;
        }

        // Users can only view their own tickets
        return $supportTicket->user_id === $user->id;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasPermissionTo('ticket-create');
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, SupportTicket $supportTicket): bool
    {
        // Admins can update any ticket
        if ($user->hasAnyRole(['super-admin', 'master-reseller'])) {
            return true;
        }

        // Users can only update their own open tickets
        return $supportTicket->user_id === $user->id &&
               $supportTicket->status === SupportTicket::STATUS_OPEN &&
               $user->hasPermissionTo('ticket-edit');
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, SupportTicket $supportTicket): bool
    {
        // Only super-admin can delete tickets
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can manage tickets (admin functions).
     */
    public function manage(User $user): bool
    {
        return $user->hasPermissionTo('ticket-admin');
    }

    /**
     * Determine whether the user can assign tickets.
     */
    public function assign(User $user, SupportTicket $supportTicket): bool
    {
        return $user->hasPermissionTo('ticket-admin');
    }

    /**
     * Determine whether the user can view admin statistics.
     */
    public function viewStatistics(User $user): bool
    {
        return $user->hasPermissionTo('ticket-admin');
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, SupportTicket $supportTicket): bool
    {
        return $user->hasRole('super-admin');
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, SupportTicket $supportTicket): bool
    {
        return $user->hasRole('super-admin');
    }
}
