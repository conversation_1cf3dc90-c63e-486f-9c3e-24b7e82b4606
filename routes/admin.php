<?php

use App\Http\Controllers\AdminDashboardController;
use App\Http\Controllers\CustomerController;
use App\Http\Controllers\GatewayController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\RechargeController;
use App\Http\Controllers\RoleController;
use App\Http\Controllers\ServerController;
use App\Http\Controllers\SupportTicketController;

Route::prefix('admin')->name('admin.')->middleware(['auth', 'verified'])->group(function () {

    // Admin Dashboard Routes
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');
    Route::get('/dashboard/alerts', [AdminDashboardController::class, 'getAlerts'])->name('dashboard.alerts');
    Route::get('/dashboard/metrics', [AdminDashboardController::class, 'getMetrics'])->name('dashboard.metrics');
    Route::post('/dashboard/quick-action', [AdminDashboardController::class, 'quickAction'])->name('dashboard.quick-action');

    Route::get('/assign-masking-number', [CustomerController::class, 'showAssignForm'])->name('assign.masking.get');
    Route::post('/assign-masking-number', [CustomerController::class, 'assignMaskingNumber'])->name('assign.masking.post');

    Route::resource('servers', ServerController::class);
    Route::get('servers/gateway-config', [ServerController::class, 'getGatewayConfig'])->name('servers.gateway-config');
    Route::post('servers/{server}/test', [ServerController::class, 'testConfiguration'])->name('servers.test');
    Route::resource('gateways', GatewayController::class);
    Route::resource('roles', RoleController::class);
    Route::resource('permission', PermissionController::class);
    Route::resource('recharges', RechargeController::class);

    // Support Ticket Admin Routes
    Route::prefix('support-tickets')->name('support-tickets.')->group(function () {
        Route::get('/', [SupportTicketController::class, 'adminIndex'])->name('index');
        Route::get('/data', [SupportTicketController::class, 'getAdminTickets'])->name('data');
        Route::get('/statistics', [SupportTicketController::class, 'getStatistics'])->name('statistics');
        Route::get('/{supportTicket}', [SupportTicketController::class, 'adminShow'])->name('show');
        Route::get('/{supportTicket}/edit', [SupportTicketController::class, 'adminEdit'])->name('edit');
        Route::put('/{supportTicket}', [SupportTicketController::class, 'adminUpdate'])->name('update');
        Route::delete('/{supportTicket}', [SupportTicketController::class, 'destroy'])->name('destroy');
    });
});
