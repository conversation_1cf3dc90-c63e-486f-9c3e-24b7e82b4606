<?php

use App\Http\Controllers\ContactController;
use App\Http\Controllers\CoverageController;
use App\Http\Controllers\GatewayController;
use App\Http\Controllers\RechargeController;
use App\Http\Controllers\ReportController;
use App\Http\Controllers\SenderController;
use App\Http\Controllers\ServerController;
use App\Http\Controllers\SupportTicketController;
use App\Http\Controllers\TemplateController;
use App\Http\Controllers\TransactionController;
use App\Http\Controllers\UserController;
use Illuminate\Support\Facades\Route;

/* ----------------- Datatable APIs ---------------------- */

Route::name('datatables.')->middleware(['auth', 'verified'])->group(function () {
    Route::get('/gateway-list', [GatewayController::class, 'browse'])->name('gateway.browse');
    Route::get('/user-list', [UserController::class, 'browse'])->name('browse');
    Route::get('/sender-list', [SenderController::class, 'browse'])->name('sender.browse');
    Route::get('/contact-list', [ContactController::class, 'browse'])->name('contact.browse');
    Route::get('/coverage-list', [CoverageController::class, 'browse'])->name('coverage.browse');
    Route::get('/server-list', [ServerController::class, 'browse'])->name('server.browse');
    Route::get('/recharge-list', [RechargeController::class, 'browse'])->name('recharge.browse');
    Route::get('/account-recharge-list', [RechargeController::class, 'list'])->name('account-recharge.list');
    Route::get('/template-list', [TemplateController::class, 'browse'])->name('template.browse');
    Route::get('/content-list', [TemplateController::class, 'getTemplate'])->name('content.list');
    Route::get('/today-detail-list', [ReportController::class, 'todayDetail'])->name('today.detail.list');
    Route::get('/transaction-list', [TransactionController::class, 'browse'])->name('transaction.browse');
    Route::get('/support-tickets-list', [SupportTicketController::class, 'getUserTickets'])->name('support-tickets.browse');
    Route::get('/admin-support-tickets-list', [SupportTicketController::class, 'getAdminTickets'])->name('admin.support-tickets.browse');
});
