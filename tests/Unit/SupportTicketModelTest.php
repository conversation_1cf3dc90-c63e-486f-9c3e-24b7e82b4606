<?php

namespace Tests\Unit;

use App\Models\SupportTicket;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class SupportTicketModelTest extends TestCase
{
    use RefreshDatabase;

    public function test_support_ticket_can_be_created(): void
    {
        $user = User::factory()->create();

        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'subject' => 'Test Ticket',
            'description' => 'Test description',
            'priority' => SupportTicket::PRIORITY_MEDIUM,
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $this->assertInstanceOf(SupportTicket::class, $ticket);
        $this->assertEquals('Test Ticket', $ticket->subject);
        $this->assertEquals(SupportTicket::STATUS_OPEN, $ticket->status);
        $this->assertEquals(SupportTicket::PRIORITY_MEDIUM, $ticket->priority);
    }

    public function test_support_ticket_belongs_to_user(): void
    {
        $user = User::factory()->create();

        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'subject' => 'Test Ticket',
            'description' => 'Test description',
            'priority' => SupportTicket::PRIORITY_MEDIUM,
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $this->assertEquals($user->id, $ticket->user->id);
        $this->assertEquals($user->name, $ticket->user->name);
    }

    public function test_support_ticket_status_methods(): void
    {
        $user = User::factory()->create();

        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'subject' => 'Test Ticket',
            'description' => 'Test description',
            'priority' => SupportTicket::PRIORITY_MEDIUM,
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $this->assertTrue($ticket->isOpen());
        $this->assertFalse($ticket->isResolved());
        $this->assertFalse($ticket->isClosed());

        $ticket->markAsResolved();
        $ticket->refresh();

        $this->assertFalse($ticket->isOpen());
        $this->assertTrue($ticket->isResolved());
        $this->assertNotNull($ticket->resolved_at);
    }

    public function test_support_ticket_badge_classes(): void
    {
        $user = User::factory()->create();

        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'subject' => 'Test Ticket',
            'description' => 'Test description',
            'priority' => SupportTicket::PRIORITY_HIGH,
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $this->assertEquals('badge-light-primary', $ticket->status_badge_class);
        $this->assertEquals('badge-light-warning', $ticket->priority_badge_class);
    }

    public function test_support_ticket_constants(): void
    {
        $statuses = SupportTicket::getStatuses();
        $priorities = SupportTicket::getPriorities();

        $this->assertIsArray($statuses);
        $this->assertIsArray($priorities);
        $this->assertArrayHasKey(SupportTicket::STATUS_OPEN, $statuses);
        $this->assertArrayHasKey(SupportTicket::PRIORITY_MEDIUM, $priorities);
    }
}
