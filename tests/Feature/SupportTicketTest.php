<?php

namespace Tests\Feature;

use App\Models\SupportTicket;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class SupportTicketTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'client']);
        Role::create(['name' => 'super-admin']);

        // Create permissions
        \Spatie\Permission\Models\Permission::create(['name' => 'ticket-list']);
        \Spatie\Permission\Models\Permission::create(['name' => 'ticket-create']);
        \Spatie\Permission\Models\Permission::create(['name' => 'ticket-edit']);
        \Spatie\Permission\Models\Permission::create(['name' => 'ticket-admin']);

        // Assign permissions to roles
        $clientRole = Role::where('name', 'client')->first();
        $clientRole->givePermissionTo(['ticket-list', 'ticket-create', 'ticket-edit']);

        $adminRole = Role::where('name', 'super-admin')->first();
        $adminRole->givePermissionTo(['ticket-list', 'ticket-create', 'ticket-edit', 'ticket-admin']);
    }

    public function test_user_can_create_support_ticket(): void
    {
        $user = User::factory()->create();
        $user->assignRole('client');

        $ticketData = [
            'subject' => 'Test Support Ticket',
            'description' => 'This is a test support ticket description.',
            'priority' => 'medium',
        ];

        $response = $this->actingAs($user)
            ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post(route('account.support-tickets.store'), $ticketData);

        $response->assertRedirect(route('account.support-tickets'));
        $this->assertDatabaseHas('support_tickets', [
            'user_id' => $user->id,
            'subject' => 'Test Support Ticket',
            'status' => SupportTicket::STATUS_OPEN,
        ]);
    }

    public function test_user_can_view_their_own_tickets(): void
    {
        $user = User::factory()->create();
        $user->assignRole('client');

        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'subject' => 'Test Ticket',
            'description' => 'Test description',
            'priority' => 'medium',
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $response = $this->actingAs($user)
            ->get(route('account.support-tickets.show', $ticket));

        $response->assertStatus(200);
        $response->assertSee('Test Ticket');
    }

    public function test_user_cannot_view_other_users_tickets(): void
    {
        $user1 = User::factory()->create();
        $user1->assignRole('client');

        $user2 = User::factory()->create();
        $user2->assignRole('client');

        $ticket = SupportTicket::create([
            'user_id' => $user2->id,
            'subject' => 'Private Ticket',
            'description' => 'Private description',
            'priority' => 'medium',
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $response = $this->actingAs($user1)
            ->get(route('account.support-tickets.show', $ticket));

        $response->assertStatus(403);
    }

    public function test_admin_can_view_all_tickets(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        $user = User::factory()->create();
        $user->assignRole('client');

        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'subject' => 'User Ticket',
            'description' => 'User description',
            'priority' => 'medium',
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $response = $this->actingAs($admin)
            ->get(route('admin.support-tickets.show', $ticket));

        $response->assertStatus(200);
        $response->assertSee('User Ticket');
    }

    public function test_admin_can_update_ticket_status(): void
    {
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        $user = User::factory()->create();
        $user->assignRole('client');

        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'subject' => 'Test Ticket',
            'description' => 'Test description',
            'priority' => 'medium',
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $updateData = [
            'status' => SupportTicket::STATUS_RESOLVED,
            'priority' => 'medium',
            'admin_notes' => 'Issue resolved',
        ];

        $response = $this->actingAs($admin)
            ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->put(route('admin.support-tickets.update', $ticket), $updateData);

        $response->assertRedirect(route('admin.support-tickets.show', $ticket));
        $this->assertDatabaseHas('support_tickets', [
            'id' => $ticket->id,
            'status' => SupportTicket::STATUS_RESOLVED,
            'admin_notes' => 'Issue resolved',
        ]);
    }
}
