<?php

namespace Tests\Feature;

use App\Mail\NewSupportTicketMail;
use App\Models\SupportTicket;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\Mail;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class SupportTicketEmailTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'client']);

        // Create permissions
        \Spatie\Permission\Models\Permission::create(['name' => 'ticket-list']);
        \Spatie\Permission\Models\Permission::create(['name' => 'ticket-create']);
        \Spatie\Permission\Models\Permission::create(['name' => 'ticket-edit']);

        // Assign permissions to roles
        $clientRole = Role::where('name', 'client')->first();
        $clientRole->givePermissionTo(['ticket-list', 'ticket-create', 'ticket-edit']);
    }

    public function test_email_is_sent_when_support_ticket_is_created(): void
    {
        Mail::fake();

        $user = User::factory()->create();
        $user->assignRole('client');

        $ticketData = [
            'subject' => 'Test Support Ticket',
            'description' => 'This is a test support ticket description.',
            'priority' => 'medium',
        ];

        $response = $this->actingAs($user)
            ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post(route('account.support-tickets.store'), $ticketData);

        $response->assertRedirect(route('account.support-tickets'));

        // Assert that an email was sent
        Mail::assertSent(NewSupportTicketMail::class, function ($mail) {
            return $mail->hasTo(config('mail.admin_email', '<EMAIL>'));
        });
    }

    public function test_email_contains_correct_ticket_information(): void
    {
        $user = User::factory()->create();
        $user->assignRole('client');

        $ticket = SupportTicket::create([
            'user_id' => $user->id,
            'subject' => 'Test Email Ticket',
            'description' => 'This is a test description for email',
            'priority' => 'high',
            'status' => SupportTicket::STATUS_OPEN,
        ]);

        $mail = new NewSupportTicketMail($ticket);

        $this->assertEquals('New Support Ticket Created - #' . $ticket->id, $mail->envelope()->subject);

        // Test that the mail can be built without errors
        $this->assertInstanceOf(NewSupportTicketMail::class, $mail);
    }

    public function test_email_failure_does_not_prevent_ticket_creation(): void
    {
        // Mock Mail to throw an exception
        Mail::shouldReceive('to')->andThrow(new \Exception('Mail server error'));

        $user = User::factory()->create();
        $user->assignRole('client');

        $ticketData = [
            'subject' => 'Test Support Ticket',
            'description' => 'This is a test support ticket description.',
            'priority' => 'medium',
        ];

        $response = $this->actingAs($user)
            ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post(route('account.support-tickets.store'), $ticketData);

        // Ticket creation should still succeed even if email fails
        $response->assertRedirect(route('account.support-tickets'));
        $this->assertDatabaseHas('support_tickets', [
            'user_id' => $user->id,
            'subject' => 'Test Support Ticket',
        ]);
    }
}
