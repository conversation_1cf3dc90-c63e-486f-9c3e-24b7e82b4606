<?php

namespace Tests\Feature;

use App\Models\User;
use App\Models\SupportTicket;
use App\Models\Payment;
use App\Models\Sender;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use Spatie\Permission\Models\Role;

class AdminDashboardTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected function setUp(): void
    {
        parent::setUp();

        // Create roles
        Role::create(['name' => 'super-admin']);
        Role::create(['name' => 'master-reseller']);
        Role::create(['name' => 'client']);
    }

    /** @test */
    public function admin_can_access_dashboard()
    {
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        $response = $this->actingAs($admin)->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard.index');
    }

    /** @test */
    public function non_admin_cannot_access_dashboard()
    {
        $user = User::factory()->create();
        $user->assignRole('client');

        $response = $this->actingAs($user)->get(route('admin.dashboard'));

        $response->assertStatus(403);
    }

    /** @test */
    public function dashboard_shows_critical_alerts()
    {
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        // Create critical support ticket
        SupportTicket::factory()->create([
            'priority' => 'critical',
            'status' => 'open'
        ]);

        $response = $this->actingAs($admin)->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertSee('Critical Support Tickets');
    }

    /** @test */
    public function dashboard_alerts_api_returns_json()
    {
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        $response = $this->actingAs($admin)->get(route('admin.dashboard.alerts'));

        $response->assertStatus(200);
        $response->assertJsonStructure([]);
    }

    /** @test */
    public function dashboard_metrics_api_returns_json()
    {
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        $response = $this->actingAs($admin)->get(route('admin.dashboard.metrics'));

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'critical_tickets',
            'high_priority_tickets',
            'open_tickets',
            'pending_payments',
            'pending_payment_amount',
            'pending_senders',
            'failed_domains',
            'pending_domains',
            'new_users_today',
            'unverified_users',
            'last_updated'
        ]);
    }

    /** @test */
    public function dashboard_shows_pending_payments_alert()
    {
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        // Create pending payment
        Payment::factory()->create([
            'payment_status' => 'pending',
            'amount' => 1000
        ]);

        $response = $this->actingAs($admin)->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertSee('Pending Payment Approvals');
    }

    /** @test */
    public function dashboard_shows_pending_senders_alert()
    {
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        // Create pending sender
        Sender::factory()->create([
            'status' => 'Pending Approved'
        ]);

        $response = $this->actingAs($admin)->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertSee('Sender ID Approvals');
    }

    /** @test */
    public function master_reseller_can_access_dashboard()
    {
        $masterReseller = User::factory()->create();
        $masterReseller->assignRole('master-reseller');

        $response = $this->actingAs($masterReseller)->get(route('admin.dashboard'));

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard.index');
    }

    /** @test */
    public function dashboard_quick_action_requires_authentication()
    {
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post(route('admin.dashboard.quick-action'), [
                'type' => 'update_ticket',
                'id' => 1,
                'action' => 'in_progress'
            ]);

        $response->assertStatus(302); // Should redirect to login
    }

    /** @test */
    public function dashboard_quick_action_validates_input()
    {
        $admin = User::factory()->create();
        $admin->assignRole('super-admin');

        $response = $this->actingAs($admin)
            ->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post(route('admin.dashboard.quick-action'), [
                'type' => 'invalid_type',
                'id' => 1,
                'action' => 'test'
            ]);

        $response->assertStatus(422);
    }
}
