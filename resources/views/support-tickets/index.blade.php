@extends('layouts.app')

@section('content')
    @php
        $user = Auth::user();
    @endphp
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        @include('account.navbar')
        <!--begin::details View-->
        <div class="card mb-5 mb-xl-10">
            <!--begin::Card header-->
            <div class="card-header cursor-pointer">
                <!--begin::Card title-->
                <div class="card-title m-0">
                    <h3 class="fw-bold m-0">Support Tickets</h3>
                </div>
                <!--end::Card title-->
                <!--begin::Action-->
                <a href="{{route('account.support-tickets.create')}}" class="btn btn-sm btn-primary align-self-center">
                    <i class="ki-duotone ki-plus fs-2"></i>
                    Create Ticket
                </a>
                <!--end::Action-->
            </div>
            <!--begin::Card header-->
            <!--begin::Card body-->
            <div class="card-body p-9">
                <div class="table-responsive">
                    <!--begin::Table-->
                    <table class="table align-middle table-row-dashed fs-6 gy-5" id="kt_table_support_tickets">
                        <!--begin::Table head-->
                        <thead>
                            <!--begin::Table row-->
                            <tr class="text-start text-muted fw-bold fs-7 text-uppercase gs-0">
                                <th class="min-w-125px">Subject</th>
                                <th class="min-w-100px">Status</th>
                                <th class="min-w-100px">Priority</th>
                                <th class="min-w-125px">Created</th>
                                <th class="text-end min-w-100px">Actions</th>
                            </tr>
                            <!--end::Table row-->
                        </thead>
                        <!--begin::Table body-->
                        <tbody class="text-gray-600 fw-semibold">
                        </tbody>
                        <!--end::Table body-->
                    </table>
                    <!--end::Table-->
                </div>
            </div>
            <!--end::Card body-->
        </div>
        <!--end::details View-->
    </div>
    <!--end::Container-->
@endsection

@section('js')
<script>
$(document).ready(function() {
    $('#kt_table_support_tickets').DataTable({
        processing: true,
        serverSide: true,
        ajax: {
            url: "{{ route('datatables.support-tickets.browse') }}",
            type: 'GET'
        },
        columns: [
            {
                data: 'subject',
                name: 'subject',
                render: function(data, type, row) {
                    return '<div class="d-flex flex-column">' +
                           '<span class="text-gray-800 fw-bold">' + data + '</span>' +
                           '<span class="text-muted fs-7">' + (row.description.length > 100 ? row.description.substring(0, 100) + '...' : row.description) + '</span>' +
                           '</div>';
                }
            },
            {
                data: 'status',
                name: 'status',
                orderable: false
            },
            {
                data: 'priority',
                name: 'priority',
                orderable: false
            },
            {
                data: 'created_at',
                name: 'created_at'
            },
            {
                data: 'action',
                name: 'action',
                orderable: false,
                searchable: false,
                className: 'text-end'
            }
        ],
        order: [[3, 'desc']],
        pageLength: 25,
        responsive: true,
        dom: '<"row"<"col-sm-12 col-md-6"l><"col-sm-12 col-md-6"f>>' +
             '<"row"<"col-sm-12"tr>>' +
             '<"row"<"col-sm-12 col-md-5"i><"col-sm-12 col-md-7"p>>',
        language: {
            emptyTable: "No support tickets found. <a href='{{ route('account.support-tickets.create') }}' class='btn btn-sm btn-primary'>Create your first ticket</a>",
            zeroRecords: "No matching tickets found."
        }
    });
});
</script>
@endsection
