@extends('layouts.app')

@section('content')
    @php
        $statusLabels = \App\Models\SupportTicket::getStatuses();
        $priorityLabels = \App\Models\SupportTicket::getPriorities();
    @endphp
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">
        <!--begin::details View-->
        <div class="card mb-5 mb-xl-10">
            <!--begin::Card header-->
            <div class="card-header cursor-pointer">
                <!--begin::Card title-->
                <div class="card-title m-0">
                    <h3 class="fw-bold m-0">Manage Support Ticket #{{ $supportTicket->id }}</h3>
                </div>
                <!--end::Card title-->
                <!--begin::Action-->
                <a href="{{route('admin.support-tickets.show', $supportTicket)}}" class="btn btn-sm btn-secondary align-self-center">
                    <i class="ki-duotone ki-arrow-left fs-2"></i>
                    Back to Ticket
                </a>
                <!--end::Action-->
            </div>
            <!--begin::Card header-->
            <!--begin::Card body-->
            <div class="card-body p-9">
                <!--begin::Customer Info-->
                <div class="card bg-light-info mb-6">
                    <div class="card-body">
                        <h5 class="card-title">Customer Information</h5>
                        <div class="row">
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Name:</strong> {{ $supportTicket->user->name }}</p>
                                <p class="mb-1"><strong>Email:</strong> {{ $supportTicket->user->email }}</p>
                                <p class="mb-0"><strong>Phone:</strong> {{ $supportTicket->user->phone }}</p>
                            </div>
                            <div class="col-md-6">
                                <p class="mb-1"><strong>Role:</strong> {{ $supportTicket->user->roles->first()->name ?? 'No Role' }}</p>
                                <p class="mb-1"><strong>Created:</strong> {{ $supportTicket->created_at->format('M d, Y h:i A') }}</p>
                                <p class="mb-0"><strong>Subject:</strong> {{ $supportTicket->subject }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                <!--end::Customer Info-->

                <!--begin::Original Issue-->
                <div class="card bg-light-primary mb-6">
                    <div class="card-body">
                        <h5 class="card-title">Original Issue Description</h5>
                        <div class="text-gray-800" style="white-space: pre-wrap;">{{ $supportTicket->description }}</div>
                    </div>
                </div>
                <!--end::Original Issue-->

                <!--begin::Form-->
                <form method="POST" action="{{ route('admin.support-tickets.update', $supportTicket) }}" class="form">
                    @csrf
                    @method('PUT')

                    <!--begin::Row-->
                    <div class="row mb-6">
                        <!--begin::Label-->
                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Status</label>
                        <!--end::Label-->
                        <!--begin::Col-->
                        <div class="col-lg-8">
                            <!--begin::Row-->
                            <div class="row">
                                <!--begin::Col-->
                                <div class="col-lg-12 fv-row">
                                    <select name="status" class="form-select form-select-solid @error('status') is-invalid @enderror" required>
                                        @foreach($statusLabels as $key => $label)
                                            <option value="{{ $key }}" {{ old('status', $supportTicket->status) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @error('status')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <!--end::Col-->
                            </div>
                            <!--end::Row-->
                        </div>
                        <!--end::Col-->
                    </div>
                    <!--end::Row-->

                    <!--begin::Row-->
                    <div class="row mb-6">
                        <!--begin::Label-->
                        <label class="col-lg-4 col-form-label required fw-semibold fs-6">Priority</label>
                        <!--end::Label-->
                        <!--begin::Col-->
                        <div class="col-lg-8">
                            <!--begin::Row-->
                            <div class="row">
                                <!--begin::Col-->
                                <div class="col-lg-12 fv-row">
                                    <select name="priority" class="form-select form-select-solid @error('priority') is-invalid @enderror" required>
                                        @foreach($priorityLabels as $key => $label)
                                            <option value="{{ $key }}" {{ old('priority', $supportTicket->priority) == $key ? 'selected' : '' }}>{{ $label }}</option>
                                        @endforeach
                                    </select>
                                    @error('priority')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <!--end::Col-->
                            </div>
                            <!--end::Row-->
                        </div>
                        <!--end::Col-->
                    </div>
                    <!--end::Row-->

                    <!--begin::Row-->
                    <div class="row mb-6">
                        <!--begin::Label-->
                        <label class="col-lg-4 col-form-label fw-semibold fs-6">Assign To</label>
                        <!--end::Label-->
                        <!--begin::Col-->
                        <div class="col-lg-8">
                            <!--begin::Row-->
                            <div class="row">
                                <!--begin::Col-->
                                <div class="col-lg-12 fv-row">
                                    <select name="assigned_to" class="form-select form-select-solid @error('assigned_to') is-invalid @enderror">
                                        <option value="">Unassigned</option>
                                        @foreach($adminUsers as $admin)
                                            <option value="{{ $admin->id }}" {{ old('assigned_to', $supportTicket->assigned_to) == $admin->id ? 'selected' : '' }}>
                                                {{ $admin->name }} ({{ $admin->email }})
                                            </option>
                                        @endforeach
                                    </select>
                                    @error('assigned_to')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>
                                <!--end::Col-->
                            </div>
                            <!--end::Row-->
                        </div>
                        <!--end::Col-->
                    </div>
                    <!--end::Row-->

                    <!--begin::Row-->
                    <div class="row mb-6">
                        <!--begin::Label-->
                        <label class="col-lg-4 col-form-label fw-semibold fs-6">Admin Response/Notes</label>
                        <!--end::Label-->
                        <!--begin::Col-->
                        <div class="col-lg-8">
                            <!--begin::Row-->
                            <div class="row">
                                <!--begin::Col-->
                                <div class="col-lg-12 fv-row">
                                    <textarea name="admin_notes" class="form-control form-control-lg form-control-solid @error('admin_notes') is-invalid @enderror"
                                              rows="8" placeholder="Add your response or internal notes here...">{{ old('admin_notes', $supportTicket->admin_notes) }}</textarea>
                                    @error('admin_notes')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div class="form-text">This response will be visible to the customer. Use this to communicate resolution steps or ask for additional information.</div>
                                </div>
                                <!--end::Col-->
                            </div>
                            <!--end::Row-->
                        </div>
                        <!--end::Col-->
                    </div>
                    <!--end::Row-->

                    <!--begin::Notice-->
                    <div class="notice d-flex bg-light-success rounded border-success border border-dashed p-6 mb-6">
                        <i class="ki-duotone ki-information fs-2tx text-success me-4">
                            <span class="path1"></span>
                            <span class="path2"></span>
                            <span class="path3"></span>
                        </i>
                        <div class="d-flex flex-stack flex-grow-1">
                            <div class="fw-semibold">
                                <h4 class="text-gray-900 fw-bold">Admin Guidelines</h4>
                                <div class="fs-6 text-gray-700">
                                    <ul class="mb-0">
                                        <li>Always provide clear and helpful responses to customers</li>
                                        <li>Update the status appropriately as you work on the ticket</li>
                                        <li>Assign tickets to the appropriate team member</li>
                                        <li>Mark tickets as resolved only when the issue is completely addressed</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--end::Notice-->

                    <!--begin::Actions-->
                    <div class="card-footer d-flex justify-content-end py-6 px-9">
                        <a href="{{ route('admin.support-tickets.show', $supportTicket) }}" class="btn btn-secondary me-2">Cancel</a>
                        <button type="submit" class="btn btn-primary">
                            <span class="indicator-label">Update Ticket</span>
                            <span class="indicator-progress">Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        </button>
                    </div>
                    <!--end::Actions-->
                </form>
                <!--end::Form-->
            </div>
            <!--end::Card body-->
        </div>
        <!--end::details View-->
    </div>
    <!--end::Container-->
@endsection
