@extends('layouts.app')

@section('title', 'Admin Dashboard')

@push('styles')
<link href="{{ asset('css/admin-dashboard.css') }}" rel="stylesheet" type="text/css" />
@endpush

@section('content')
    <!--begin::Container-->
    <div class="container-xxl" id="kt_content_container">

        <!-- Critical Alerts Section -->
        @if(count($criticalAlerts) > 0)
            <div class="alert-section mb-5" id="critical-alerts">
                @foreach($criticalAlerts as $alert)
                    <div class="alert alert-dismissible bg-light-{{ $alert['type'] === 'critical' ? 'danger' : ($alert['type'] === 'financial' ? 'warning' : 'info') }} d-flex flex-column flex-sm-row p-5 mb-3">
                        <!--begin::Icon-->
                        <i class="{{ $alert['icon'] }} fs-2hx text-{{ $alert['type'] === 'critical' ? 'danger' : ($alert['type'] === 'financial' ? 'warning' : 'info') }} me-4 mb-5 mb-sm-0"></i>
                        <!--end::Icon-->

                        <!--begin::Wrapper-->
                        <div class="d-flex flex-column pe-0 pe-sm-10">
                            <!--begin::Title-->
                            <h4 class="fw-semibold">{{ $alert['title'] }}</h4>
                            <!--end::Title-->
                            <!--begin::Content-->
                            <span>{{ $alert['message'] }}</span>
                            <!--end::Content-->
                        </div>
                        <!--end::Wrapper-->

                        <!--begin::Action-->
                        <a href="{{ $alert['action_url'] }}" class="btn btn-{{ $alert['type'] === 'critical' ? 'danger' : ($alert['type'] === 'financial' ? 'warning' : 'info') }} ms-auto">
                            {{ $alert['action_text'] }}
                        </a>
                        <!--end::Action-->
                    </div>
                @endforeach
            </div>
        @endif

        <!-- Summary Cards Row -->
        <div class="row g-5 g-xl-10 mb-5 mb-xl-10">
            @foreach($summaryCards as $card)
                <div class="col-sm-6 col-xl-3">
                    <!--begin::Card widget-->
                    <div class="card card-flush border-0 h-lg-100" data-bs-theme="light" style="background: linear-gradient(112.14deg, {{ $card['color'] === 'success' ? '#00A3FF 0%, #0077B6 100%' : ($card['color'] === 'warning' ? '#FFB800 0%, #FF8A00 100%' : '#F1416C 0%, #E4002B 100%') }})">
                        <!--begin::Header-->
                        <div class="card-header pt-5">
                            <!--begin::Title-->
                            <div class="card-title d-flex flex-column">
                                <!--begin::Amount-->
                                <span class="fs-2hx fw-bold text-white me-2 lh-1 ls-n2">{{ $card['value'] }}</span>
                                <!--end::Amount-->
                                <!--begin::Subtitle-->
                                <span class="text-white opacity-75 pt-1 fw-semibold fs-6">{{ $card['title'] }}</span>
                                <!--end::Subtitle-->
                            </div>
                            <!--end::Title-->
                        </div>
                        <!--end::Header-->
                        <!--begin::Card body-->
                        <div class="card-body d-flex flex-column justify-content-end pe-0">
                            <!--begin::Title-->
                            <span class="fs-6 fw-bolder text-white opacity-75 pb-1">{{ $card['subtitle'] }}</span>
                            <!--end::Title-->
                            <!--begin::Progress-->
                            <div class="d-flex align-items-center">
                                <div class="border border-white border-dashed rounded min-w-40px py-3 px-4 me-6 mb-3">
                                    <!--begin::Icon-->
                                    <i class="{{ $card['icon'] }} fs-2 text-white"></i>
                                    <!--end::Icon-->
                                </div>
                                <!--begin::Action-->
                                <a href="{{ $card['action_url'] }}" class="btn btn-sm btn-light text-dark fw-bold ms-auto mb-3">View Details</a>
                                <!--end::Action-->
                            </div>
                            <!--end::Progress-->
                        </div>
                        <!--end::Card body-->
                    </div>
                    <!--end::Card widget-->
                </div>
            @endforeach
        </div>

        <!-- Main Content Row -->
        <div class="row g-5 g-xl-10">
            <!-- Action Queue (Left Column) -->
            <div class="col-xl-8">
                <!--begin::Card-->
                <div class="card card-flush h-lg-100">
                    <!--begin::Header-->
                    <div class="card-header pt-5">
                        <!--begin::Title-->
                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold text-dark">Priority Action Queue</span>
                            <span class="text-gray-400 mt-1 fw-semibold fs-6">Items requiring immediate attention</span>
                        </h3>
                        <!--end::Title-->
                        <!--begin::Toolbar-->
                        <div class="card-toolbar">
                            <button type="button" class="btn btn-sm btn-light-primary" onclick="refreshActionQueue()">
                                <i class="ki-duotone ki-arrows-circle fs-2">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                Refresh
                            </button>
                        </div>
                        <!--end::Toolbar-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Body-->
                    <div class="card-body pt-5" id="action-queue-container">
                        @if(count($actionQueue) > 0)
                            @foreach($actionQueue as $item)
                                <div class="d-flex align-items-center border border-dashed border-gray-300 rounded min-w-750px px-7 py-3 mb-5">
                                    <!--begin::Priority indicator-->
                                    <div class="w-50px">
                                        <div class="badge badge-light-{{ $item['urgency_class'] }} fw-bold fs-8 px-2 py-1 ms-2">
                                            P{{ $item['priority'] }}
                                        </div>
                                    </div>
                                    <!--end::Priority indicator-->

                                    <!--begin::Info-->
                                    <div class="flex-grow-1 me-2">
                                        <!--begin::Title-->
                                        <a href="#" class="text-gray-800 text-hover-primary fs-6 fw-bold">{{ $item['title'] }}</a>
                                        <!--end::Title-->
                                        <!--begin::Description-->
                                        <span class="text-gray-400 fw-semibold d-block fs-7">{{ $item['description'] }}</span>
                                        <!--end::Description-->
                                        <!--begin::User and time-->
                                        <span class="text-gray-400 fw-semibold d-block fs-8">
                                            {{ $item['user'] }} • {{ $item['created_at']->diffForHumans() }}
                                        </span>
                                        <!--end::User and time-->
                                    </div>
                                    <!--end::Info-->

                                    <!--begin::Actions-->
                                    <div class="d-flex align-items-center">
                                        @foreach($item['actions'] as $action)
                                            <a href="{{ $action['url'] }}" class="btn {{ $action['class'] }} btn-sm me-2">
                                                {{ $action['text'] }}
                                            </a>
                                        @endforeach
                                    </div>
                                    <!--end::Actions-->
                                </div>
                            @endforeach
                        @else
                            <div class="text-center py-10">
                                <i class="ki-duotone ki-check-circle fs-4x text-success mb-5">
                                    <span class="path1"></span>
                                    <span class="path2"></span>
                                </i>
                                <h3 class="text-gray-800 fw-bold mb-3">All caught up!</h3>
                                <p class="text-gray-400 fw-semibold fs-6">No urgent items require your attention right now.</p>
                            </div>
                        @endif
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Card-->
            </div>

            <!-- Quick Actions Panel (Right Column) -->
            <div class="col-xl-4">
                <!--begin::Card-->
                <div class="card card-flush h-lg-100">
                    <!--begin::Header-->
                    <div class="card-header pt-5">
                        <!--begin::Title-->
                        <h3 class="card-title align-items-start flex-column">
                            <span class="card-label fw-bold text-dark">Quick Actions</span>
                            <span class="text-gray-400 mt-1 fw-semibold fs-6">Common admin tasks</span>
                        </h3>
                        <!--end::Title-->
                    </div>
                    <!--end::Header-->
                    <!--begin::Body-->
                    <div class="card-body pt-5">
                        <!--begin::Item-->
                        <div class="d-flex align-items-center mb-7">
                            <!--begin::Symbol-->
                            <div class="symbol symbol-50px me-5">
                                <span class="symbol-label bg-light-primary">
                                    <i class="ki-duotone ki-questionnaire-tablet fs-2x text-primary">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                            </div>
                            <!--end::Symbol-->
                            <!--begin::Text-->
                            <div class="d-flex flex-column">
                                <a href="{{ route('admin.support-tickets.index') }}" class="text-dark text-hover-primary fs-6 fw-bold">Manage Support Tickets</a>
                                <span class="text-gray-400 fw-semibold fs-7">View and respond to customer tickets</span>
                            </div>
                            <!--end::Text-->
                        </div>
                        <!--end::Item-->

                        <!--begin::Item-->
                        <div class="d-flex align-items-center mb-7">
                            <!--begin::Symbol-->
                            <div class="symbol symbol-50px me-5">
                                <span class="symbol-label bg-light-warning">
                                    <i class="ki-duotone ki-dollar fs-2x text-warning">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                    </i>
                                </span>
                            </div>
                            <!--end::Symbol-->
                            <!--begin::Text-->
                            <div class="d-flex flex-column">
                                <a href="{{ route('admin.recharges.index') }}" class="text-dark text-hover-primary fs-6 fw-bold">Review Payments</a>
                                <span class="text-gray-400 fw-semibold fs-7">Approve pending recharge requests</span>
                            </div>
                            <!--end::Text-->
                        </div>
                        <!--end::Item-->

                        <!--begin::Item-->
                        <div class="d-flex align-items-center mb-7">
                            <!--begin::Symbol-->
                            <div class="symbol symbol-50px me-5">
                                <span class="symbol-label bg-light-info">
                                    <i class="ki-duotone ki-send fs-2x text-info">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                    </i>
                                </span>
                            </div>
                            <!--end::Symbol-->
                            <!--begin::Text-->
                            <div class="d-flex flex-column">
                                <a href="{{ route('senders.index') }}" class="text-dark text-hover-primary fs-6 fw-bold">Sender Approvals</a>
                                <span class="text-gray-400 fw-semibold fs-7">Review sender ID applications</span>
                            </div>
                            <!--end::Text-->
                        </div>
                        <!--end::Item-->

                        <!--begin::Item-->
                        <div class="d-flex align-items-center">
                            <!--begin::Symbol-->
                            <div class="symbol symbol-50px me-5">
                                <span class="symbol-label bg-light-success">
                                    <i class="ki-duotone ki-people fs-2x text-success">
                                        <span class="path1"></span>
                                        <span class="path2"></span>
                                        <span class="path3"></span>
                                        <span class="path4"></span>
                                        <span class="path5"></span>
                                    </i>
                                </span>
                            </div>
                            <!--end::Symbol-->
                            <!--begin::Text-->
                            <div class="d-flex flex-column">
                                <a href="{{ route('admin.users.index') }}" class="text-dark text-hover-primary fs-6 fw-bold">User Management</a>
                                <span class="text-gray-400 fw-semibold fs-7">Manage user accounts and roles</span>
                            </div>
                            <!--end::Text-->
                        </div>
                        <!--end::Item-->
                    </div>
                    <!--end::Body-->
                </div>
                <!--end::Card-->
            </div>
        </div>
    </div>
    <!--end::Container-->
@endsection

@push('scripts')
<script>
// Auto-refresh critical alerts every 30 seconds
setInterval(function() {
    refreshAlerts();
}, 30000);

// Auto-refresh dashboard metrics every 60 seconds
setInterval(function() {
    refreshMetrics();
}, 60000);

function refreshAlerts() {
    fetch('{{ route("admin.dashboard.alerts") }}')
        .then(response => response.json())
        .then(data => {
            updateAlertsDisplay(data);
        })
        .catch(error => console.error('Error refreshing alerts:', error));
}

function refreshMetrics() {
    fetch('{{ route("admin.dashboard.metrics") }}')
        .then(response => response.json())
        .then(data => {
            updateMetricsDisplay(data);
        })
        .catch(error => console.error('Error refreshing metrics:', error));
}

function refreshActionQueue() {
    location.reload(); // Simple refresh for now, can be improved with AJAX
}

function updateAlertsDisplay(alerts) {
    // Update alerts section - simplified for now
    if (alerts.length === 0) {
        document.getElementById('critical-alerts')?.remove();
    }
}

function updateMetricsDisplay(metrics) {
    // Update summary cards - can be enhanced to update specific values
    console.log('Metrics updated:', metrics);
}

// Show loading state for quick actions
document.addEventListener('DOMContentLoaded', function() {
    const quickActionButtons = document.querySelectorAll('[data-quick-action]');
    quickActionButtons.forEach(button => {
        button.addEventListener('click', function() {
            this.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Processing...';
            this.disabled = true;
        });
    });
});
</script>
@endpush
