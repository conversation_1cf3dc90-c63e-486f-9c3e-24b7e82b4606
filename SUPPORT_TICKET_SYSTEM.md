# Support Ticket System Documentation

## Overview

A comprehensive support ticket system has been implemented for the SMS service application, allowing users to create and manage support tickets while providing administrators with tools to efficiently handle customer support requests.

## Features

### Core Functionality
- ✅ Users can create support tickets with subject, description, and priority
- ✅ Admin users can view, manage, and resolve all tickets
- ✅ Ticket status tracking (Open, In Progress, Resolved, Closed)
- ✅ Priority levels (Low, Medium, High, Critical)
- ✅ Timestamps for ticket creation and resolution
- ✅ Admin notes/responses visible to customers
- ✅ **Email notifications to admin when new tickets are created**

### User Interface
- ✅ Responsive UI components matching existing Metronic theme
- ✅ DataTables integration for efficient ticket browsing
- ✅ Status and priority badges with color coding
- ✅ Timeline view showing ticket progression
- ✅ Quick action buttons for admins

### Email Notifications
- ✅ Automatic email notifications to admin when new tickets are created
- ✅ Professional email template with ticket details and customer information
- ✅ Priority-based email styling (Critical and High priority tickets highlighted)
- ✅ Direct link to admin ticket management interface
- ✅ Configurable admin email address via environment variables
- ✅ Graceful error handling - ticket creation succeeds even if email fails
- ✅ Queue-ready for high-volume environments

### Security & Authorization
- ✅ Role-based access control using <PERSON><PERSON>vel Permission
- ✅ Users can only see their own tickets
- ✅ <PERSON><PERSON> can see and manage all tickets
- ✅ Policy-based authorization for all actions
- ✅ Form request validation for data integrity

## Database Schema

### Support Tickets Table
```sql
CREATE TABLE support_tickets (
    id BIGINT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    user_id BIGINT UNSIGNED NOT NULL,
    subject VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    status ENUM('open', 'in_progress', 'resolved', 'closed') DEFAULT 'open',
    priority ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium',
    admin_notes TEXT NULL,
    assigned_to BIGINT UNSIGNED NULL,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP NULL,
    updated_at TIMESTAMP NULL,
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL,
    
    INDEX idx_user_status (user_id, status),
    INDEX idx_status_priority (status, priority),
    INDEX idx_created_at (created_at)
);
```

### Permissions Added
- `ticket-list` - View tickets
- `ticket-create` - Create new tickets
- `ticket-edit` - Edit existing tickets
- `ticket-delete` - Delete tickets (super-admin only)
- `ticket-admin` - Admin management functions

## File Structure

### Models
- `app/Models/SupportTicket.php` - Main ticket model with relationships and helper methods

### Controllers
- `app/Http/Controllers/SupportTicketController.php` - Handles all ticket operations

### Form Requests
- `app/Http/Requests/StoreSupportTicketRequest.php` - Validation for creating tickets
- `app/Http/Requests/UpdateSupportTicketRequest.php` - Validation for updating tickets

### Policies
- `app/Policies/SupportTicketPolicy.php` - Authorization logic for ticket operations

### Email Components
- `app/Mail/NewSupportTicketMail.php` - Mailable class for new ticket notifications
- `app/Notifications/NewSupportTicketNotification.php` - Notification class (alternative implementation)
- `app/Models/AdminNotifiable.php` - Helper class for admin email notifications
- `resources/views/emails/support-ticket/new-ticket.blade.php` - Email template

### Views
- `resources/views/support-tickets/index.blade.php` - User ticket listing
- `resources/views/support-tickets/create.blade.php` - Create new ticket form
- `resources/views/support-tickets/show.blade.php` - View ticket details
- `resources/views/support-tickets/edit.blade.php` - Edit ticket form
- `resources/views/support-tickets/admin/index.blade.php` - Admin ticket dashboard
- `resources/views/support-tickets/admin/show.blade.php` - Admin ticket view
- `resources/views/support-tickets/admin/edit.blade.php` - Admin ticket management

### Database
- `database/migrations/2025_06_28_001048_create_support_tickets_table.php` - Table migration
- `database/seeders/SupportTicketPermissionSeeder.php` - Permissions seeder

### Tests
- `tests/Unit/SupportTicketModelTest.php` - Model unit tests
- `tests/Feature/SupportTicketTest.php` - Feature tests
- `tests/Feature/SupportTicketEmailTest.php` - Email notification tests

### Commands
- `app/Console/Commands/TestSupportTicketEmail.php` - Test email functionality

## Routes

### User Routes (account.*)
```php
GET    /account/support-tickets                    - List user's tickets
GET    /account/support-tickets/create             - Create ticket form
POST   /account/support-tickets                    - Store new ticket
GET    /account/support-tickets/{ticket}           - View ticket
GET    /account/support-tickets/{ticket}/edit      - Edit ticket form
PUT    /account/support-tickets/{ticket}           - Update ticket
```

### Admin Routes (admin.support-tickets.*)
```php
GET    /admin/support-tickets                      - Admin dashboard
GET    /admin/support-tickets/data                 - DataTables data
GET    /admin/support-tickets/statistics           - Statistics API
GET    /admin/support-tickets/{ticket}             - Admin view ticket
GET    /admin/support-tickets/{ticket}/edit        - Admin edit form
PUT    /admin/support-tickets/{ticket}             - Admin update ticket
DELETE /admin/support-tickets/{ticket}             - Delete ticket
```

### DataTables Routes
```php
GET    /datatables/support-tickets-list            - User tickets data
GET    /datatables/admin-support-tickets-list      - Admin tickets data
```

## Navigation Integration

### Main Sidebar Menu
- Added "Support Tickets" menu item for all users
- Added "Manage Tickets" menu item for admins (super-admin, master-reseller)

### Account Navigation
- Added "Support Tickets" tab in account navbar

## Usage Examples

### Creating a Ticket (User)
1. Navigate to "Support Tickets" from main menu
2. Click "Create Ticket" button
3. Fill in subject, description, and priority
4. Submit form
5. Ticket is created with "Open" status

### Managing Tickets (Admin)
1. Navigate to "Manage Tickets" from main menu
2. View dashboard with statistics and ticket list
3. Click on any ticket to view details
4. Use "Manage Ticket" to update status, priority, assignment, and add notes
5. Use quick action buttons for common operations

## API Endpoints

### Statistics (Admin Only)
```php
GET /admin/support-tickets/statistics
Response: {
    "total": 150,
    "open": 25,
    "in_progress": 10,
    "resolved": 100,
    "closed": 15,
    "critical": 3,
    "high": 8
}
```

## Role Permissions

### Client Role
- Can create tickets
- Can view own tickets
- Can edit own open tickets

### Reseller Role
- Same as client role

### Master Reseller Role
- Can view all tickets
- Can manage all tickets (admin functions)

### Super Admin Role
- Full access to all ticket operations
- Can delete tickets

## Security Considerations

1. **Authorization**: All routes protected by policies
2. **Data Validation**: Comprehensive form request validation
3. **CSRF Protection**: All forms include CSRF tokens
4. **SQL Injection**: Using Eloquent ORM prevents SQL injection
5. **XSS Protection**: All user input properly escaped in views

## Performance Optimizations

1. **Database Indexes**: Added indexes on frequently queried columns
2. **Eager Loading**: Relationships loaded efficiently in controllers
3. **DataTables**: Server-side processing for large datasets
4. **Caching**: Route caching and view caching supported

## Testing

### Unit Tests
- Model creation and relationships
- Status and priority methods
- Badge class generation
- Constants and helper methods

### Feature Tests
- User ticket creation
- Authorization checks
- Admin ticket management
- Policy enforcement

## Email Configuration

### Environment Variables
Add the following to your `.env` file:

```env
# Admin Email for Support Ticket Notifications
ADMIN_EMAIL=<EMAIL>

# Mail Configuration (if not already configured)
MAIL_MAILER=smtp
MAIL_HOST=smtp-relay.brevo.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your_mail_password
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"
```

### Email Template Customization
The email template is located at `resources/views/emails/support-ticket/new-ticket.blade.php` and can be customized to match your branding.

### Testing Email Functionality
Use the provided artisan command to test email notifications:

```bash
# Create a test user first
php artisan db:seed --class=TestUserSeeder

# Test email notification
php artisan test:support-ticket-email --user-id=1
```

## Deployment Notes

1. Run migrations: `php artisan migrate`
2. Seed permissions: `php artisan db:seed --class=SupportTicketPermissionSeeder`
3. Configure email settings in `.env` file
4. Test email functionality: `php artisan test:support-ticket-email --user-id=1`
5. Clear caches: `php artisan cache:clear && php artisan route:clear`
6. Test functionality with different user roles

## Future Enhancements

Potential improvements that could be added:

1. **File Attachments**: Allow users to attach files to tickets
2. **Email Notifications**: Send emails when ticket status changes
3. **Ticket Categories**: Add categories for better organization
4. **SLA Tracking**: Track response and resolution times
5. **Ticket Templates**: Pre-defined templates for common issues
6. **Customer Satisfaction**: Rating system for resolved tickets
7. **Ticket Escalation**: Automatic escalation for overdue tickets
8. **Real-time Updates**: WebSocket integration for live updates

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure user has proper role and permissions
2. **Routes Not Found**: Clear route cache with `php artisan route:clear`
3. **Views Not Loading**: Check if all view files exist and have correct syntax
4. **DataTables Not Loading**: Verify JavaScript files are included and routes are accessible

### Debug Commands

```bash
# Check routes
php artisan route:list | grep support

# Check permissions
php artisan tinker
>>> User::find(1)->getAllPermissions()

# Clear caches
php artisan cache:clear
php artisan config:clear
php artisan route:clear
php artisan view:clear
```

## Conclusion

The support ticket system is now fully integrated into the SMS service application, providing a professional and user-friendly way for customers to get help and for administrators to manage support requests efficiently. The system follows Laravel best practices and integrates seamlessly with the existing application architecture.
