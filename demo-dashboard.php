<?php

/**
 * Admin Dashboard Demo Script
 * 
 * This script demonstrates the admin dashboard functionality
 * Run with: php demo-dashboard.php
 */

require_once 'vendor/autoload.php';

use Illuminate\Foundation\Application;
use Illuminate\Http\Request;

// Bootstrap Laravel application
$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

echo "=== Admin Dashboard Demo ===\n\n";

// Test 1: Check if routes are registered
echo "1. Testing Route Registration:\n";
try {
    $routes = \Illuminate\Support\Facades\Route::getRoutes();
    $adminDashboardRoutes = [];
    
    foreach ($routes as $route) {
        if (strpos($route->getName(), 'admin.dashboard') === 0) {
            $adminDashboardRoutes[] = $route->getName() . ' => ' . $route->uri();
        }
    }
    
    if (count($adminDashboardRoutes) > 0) {
        echo "✅ Admin dashboard routes found:\n";
        foreach ($adminDashboardRoutes as $route) {
            echo "   - $route\n";
        }
    } else {
        echo "❌ No admin dashboard routes found\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking routes: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 2: Check controller exists and methods are callable
echo "2. Testing Controller Methods:\n";
try {
    $controller = new \App\Http\Controllers\AdminDashboardController();
    
    $methods = ['index', 'getAlerts', 'getMetrics', 'quickAction'];
    foreach ($methods as $method) {
        if (method_exists($controller, $method)) {
            echo "✅ Method $method exists\n";
        } else {
            echo "❌ Method $method missing\n";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking controller: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Check view files exist
echo "3. Testing View Files:\n";
$viewFiles = [
    'resources/views/admin/dashboard/index.blade.php',
    'public/css/admin-dashboard.css'
];

foreach ($viewFiles as $file) {
    if (file_exists($file)) {
        echo "✅ $file exists\n";
    } else {
        echo "❌ $file missing\n";
    }
}

echo "\n";

// Test 4: Check model relationships
echo "4. Testing Model Classes:\n";
$models = [
    'App\Models\SupportTicket',
    'App\Models\Payment', 
    'App\Models\Sender',
    'App\Models\Domain',
    'App\Models\User'
];

foreach ($models as $model) {
    if (class_exists($model)) {
        echo "✅ $model class exists\n";
    } else {
        echo "❌ $model class missing\n";
    }
}

echo "\n";

// Test 5: Simulate dashboard data structure
echo "5. Testing Dashboard Data Structure:\n";
try {
    // Simulate the dashboard data structure
    $mockData = [
        'critical_tickets' => 2,
        'high_priority_tickets' => 5,
        'open_tickets' => 12,
        'pending_payments' => 3,
        'pending_payment_amount' => 15000.00,
        'pending_senders' => 4,
        'failed_domains' => 1,
        'pending_domains' => 2,
        'new_users_today' => 8,
        'unverified_users' => 15,
        'last_updated' => date('c')
    ];
    
    echo "✅ Dashboard metrics structure:\n";
    foreach ($mockData as $key => $value) {
        echo "   - $key: $value\n";
    }
    
    // Simulate critical alerts
    $mockAlerts = [
        [
            'type' => 'critical',
            'title' => 'Critical Support Tickets',
            'message' => '2 critical support ticket(s) require immediate attention',
            'count' => 2,
            'priority' => 1
        ],
        [
            'type' => 'financial',
            'title' => 'Pending Payment Approvals',
            'message' => '3 payment(s) pending approval (৳15,000.00)',
            'count' => 3,
            'priority' => 2
        ]
    ];
    
    echo "\n✅ Critical alerts structure:\n";
    foreach ($mockAlerts as $alert) {
        echo "   - {$alert['title']}: {$alert['message']}\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error testing data structure: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 6: Check permissions and roles
echo "6. Testing Permission System:\n";
try {
    if (class_exists('Spatie\Permission\Models\Role')) {
        echo "✅ Spatie Permission package available\n";
        echo "   - Required roles: super-admin, master-reseller\n";
        echo "   - Dashboard access restricted to admin roles\n";
    } else {
        echo "❌ Spatie Permission package not found\n";
    }
} catch (Exception $e) {
    echo "❌ Error checking permissions: " . $e->getMessage() . "\n";
}

echo "\n=== Demo Complete ===\n";
echo "\nNext Steps:\n";
echo "1. Ensure database is properly configured\n";
echo "2. Run migrations: php artisan migrate\n";
echo "3. Seed roles and permissions: php artisan db:seed\n";
echo "4. Create admin user with super-admin role\n";
echo "5. Access dashboard at: /admin/dashboard\n";
echo "\nDashboard Features Implemented:\n";
echo "✅ Priority-based alert system\n";
echo "✅ Summary cards with urgency indicators\n";
echo "✅ Action queue with one-click actions\n";
echo "✅ Real-time updates via AJAX\n";
echo "✅ Responsive design with custom CSS\n";
echo "✅ Role-based access control\n";
echo "✅ Quick actions panel\n";
echo "✅ Email notification integration ready\n";

echo "\nPriority Items Tracked:\n";
echo "🔴 Critical support tickets\n";
echo "🟡 Pending payment approvals\n";
echo "🔵 Sender ID approvals\n";
echo "🟢 System health monitoring\n";
echo "📊 User activity metrics\n";
