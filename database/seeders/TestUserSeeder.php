<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Role;

class TestUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a test client user
        $user = User::create([
            'name' => 'Test Client',
            'username' => 'testclient',
            'email' => '<EMAIL>',
            'phone' => '+1234567890',
            'password' => Hash::make('password'),
            'email_verified_at' => now(),
        ]);

        // Assign client role if it exists
        $clientRole = Role::where('name', 'client')->first();
        if ($clientRole) {
            $user->assignRole($clientRole);
        }

        echo "Created test user: {$user->name} ({$user->email}) with ID: {$user->id}\n";
    }
}
