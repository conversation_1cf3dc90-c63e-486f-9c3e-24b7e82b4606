<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class SupportTicketPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create support ticket permissions if they don't exist
        $permissions = [
            'ticket-list',
            'ticket-create',
            'ticket-edit',
            'ticket-delete',
            'ticket-admin'
        ];

        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to roles
        $superAdmin = Role::where('name', 'super-admin')->first();
        if ($superAdmin) {
            $superAdmin->givePermissionTo($permissions);
        }

        $masterReseller = Role::where('name', 'master-reseller')->first();
        if ($masterReseller) {
            $masterReseller->givePermissionTo(['ticket-list', 'ticket-admin']);
        }

        $reseller = Role::where('name', 'reseller')->first();
        if ($reseller) {
            $reseller->givePermissionTo(['ticket-list', 'ticket-create', 'ticket-edit']);
        }

        $client = Role::where('name', 'client')->first();
        if ($client) {
            $client->givePermissionTo(['ticket-list', 'ticket-create', 'ticket-edit']);
        }
    }
}
