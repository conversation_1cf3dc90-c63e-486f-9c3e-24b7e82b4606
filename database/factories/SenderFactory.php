<?php

namespace Database\Factories;

use App\Models\Sender;
use App\Models\Company;
use App\Models\Server;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Sender>
 */
class SenderFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Sender::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isMasking = $this->faker->boolean();

        return [
            'name' => $isMasking ? $this->faker->company() : $this->faker->numerify('##########'),
            'executed_at' => null,
            'company_id' => Company::factory(),
            'user_id' => User::factory(),
            'server_id' => null, // Make server_id nullable for now
            'is_masking' => $isMasking,
            'is_default' => false,
            'is_server_default' => false,
            'status' => $this->faker->randomElement(['Pending', 'Pending Approved', 'Approved', 'Disabled']),
            'required_documents' => $this->faker->optional()->randomElements([
                'trade_license.pdf',
                'nid_copy.pdf',
                'company_profile.pdf'
            ], $this->faker->numberBetween(0, 3)),
        ];
    }

    /**
     * Indicate that the sender is pending approval.
     */
    public function pendingApproval(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Pending Approved',
        ]);
    }

    /**
     * Indicate that the sender is approved.
     */
    public function approved(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'Approved',
            'executed_at' => $this->faker->dateTimeBetween('-1 month', 'now'),
        ]);
    }

    /**
     * Indicate that the sender is for masking.
     */
    public function masking(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_masking' => true,
            'name' => $this->faker->company(),
        ]);
    }

    /**
     * Indicate that the sender is non-masking (numeric).
     */
    public function nonMasking(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_masking' => false,
            'name' => $this->faker->numerify('##########'),
        ]);
    }

    /**
     * Indicate that the sender is default.
     */
    public function default(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_default' => true,
        ]);
    }
}
