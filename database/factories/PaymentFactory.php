<?php

namespace Database\Factories;

use App\Models\Payment;
use App\Models\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Payment>
 */
class PaymentFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Payment::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'amount' => $this->faker->randomFloat(2, 100, 10000),
            'gateway' => $this->faker->randomElement(['bkash', 'nagad', 'rocket', 'manual']),
            'transaction_id' => $this->faker->unique()->regexify('[A-Z0-9]{10}'),
            'user_id' => User::factory(),
            'company_id' => Company::factory(),
            'payment_status' => $this->faker->randomElement(['pending', 'completed', 'failed', 'canceled']),
            'remarks' => $this->faker->optional()->sentence(),
            'api_response' => null,
        ];
    }

    /**
     * Indicate that the payment is pending.
     */
    public function pending(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'pending',
        ]);
    }

    /**
     * Indicate that the payment is completed.
     */
    public function completed(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'completed',
            'api_response' => json_encode(['status' => 'success', 'transaction_id' => $attributes['transaction_id']]),
        ]);
    }

    /**
     * Indicate that the payment failed.
     */
    public function failed(): static
    {
        return $this->state(fn (array $attributes) => [
            'payment_status' => 'failed',
            'api_response' => json_encode(['status' => 'failed', 'error' => 'Payment declined']),
        ]);
    }

    /**
     * Set a specific amount.
     */
    public function amount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amount,
        ]);
    }
}
